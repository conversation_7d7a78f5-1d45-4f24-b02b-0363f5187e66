import { YouTubeService } from './youtube.service';
import { ConfigService } from './config.service';
import { InfluencerRepository } from '../repositories/influencer.repository';
import { Logger } from '../logger/logger';
import { 
  YouTubeChannelSearchResult, 
  YouTubeRegionCode, 
  YouTubeLanguageCode 
} from '../models/youtube.model';
import { CreateInfluencerData, Platform } from '../models/influencer.model';

export interface DiscoverySearchOptions {
  query: string;
  countries?: YouTubeRegionCode[];
  languages?: YouTubeLanguageCode[];
  minSubscribers?: number;
  maxSubscribers?: number;
  maxResults?: number;
  saveToDatabase?: boolean;
  skipExisting?: boolean;
}

export interface DiscoveryResult {
  channelsFound: number;
  channelsNew: number;
  channelsExisting: number;
  channelsSaved: number;
  errors: string[];
  channels: YouTubeChannelSearchResult[];
}

/**
 * YouTube Discovery Service
 * 
 * Combines YouTube API search capabilities with database operations
 * to discover and store influencer channel information.
 */
export class YouTubeDiscoveryService {
  private readonly logger: Logger;
  private readonly youtubeService: YouTubeService;
  private readonly influencerRepository: InfluencerRepository;
  private readonly configService: ConfigService;

  constructor(
    youtubeService: YouTubeService,
    influencerRepository: InfluencerRepository
  ) {
    this.logger = Logger.c('YouTubeDiscoveryService');
    this.youtubeService = youtubeService;
    this.influencerRepository = influencerRepository;
    this.configService = ConfigService.getInstance();

    this.logger.info('YouTube Discovery Service initialized');
  }

  /**
   * Discover YouTube channels based on search criteria and optionally save to database
   */
  async discoverChannels(options: DiscoverySearchOptions): Promise<DiscoveryResult> {
    const result: DiscoveryResult = {
      channelsFound: 0,
      channelsNew: 0,
      channelsExisting: 0,
      channelsSaved: 0,
      errors: [],
      channels: [],
    };

    try {
      this.logger.info(`Starting channel discovery for query: "${options.query}"`);

      const countries = options.countries || [YouTubeRegionCode.US];
      const languages = options.languages || [YouTubeLanguageCode.EN];

      // Search for channels in each country/language combination
      for (const country of countries) {
        for (const language of languages) {
          try {
            this.logger.info(`Searching in ${country} (${language}) for: "${options.query}"`);

            const channels = await this.youtubeService.searchChannels({
              query: options.query,
              country,
              language,
              maxResults: options.maxResults || 50,
              minSubscribers: options.minSubscribers,
              maxSubscribers: options.maxSubscribers,
              order: 'relevance',
            });

            this.logger.info(`Found ${channels.length} channels in ${country} (${language})`);
            result.channels.push(...channels);

          } catch (error) {
            const errorMsg = `Error searching in ${country} (${language}): ${String(error)}`;
            this.logger.error(errorMsg);
            result.errors.push(errorMsg);
          }
        }
      }

      // Remove duplicates based on channel ID
      result.channels = this.removeDuplicateChannels(result.channels);
      result.channelsFound = result.channels.length;

      this.logger.info(`Total unique channels found: ${result.channelsFound}`);

      // Save to database if requested
      if (options.saveToDatabase) {
        const saveResult = await this.saveChannelsToDatabase(result.channels, options.skipExisting);
        result.channelsNew = saveResult.new;
        result.channelsExisting = saveResult.existing;
        result.channelsSaved = saveResult.saved;
        result.errors.push(...saveResult.errors);
      }

      this.logger.info('Channel discovery completed', {
        found: result.channelsFound,
        new: result.channelsNew,
        existing: result.channelsExisting,
        saved: result.channelsSaved,
        errors: result.errors.length,
      });

      return result;

    } catch (error) {
      const errorMsg = `Error in channel discovery: ${String(error)}`;
      this.logger.error(errorMsg);
      result.errors.push(errorMsg);
      return result;
    }
  }

  /**
   * Discover channels from a specific country with minimum subscriber count
   */
  async discoverChannelsFromCountry(
    query: string,
    country: YouTubeRegionCode,
    minSubscribers: number = 1000,
    options: Partial<DiscoverySearchOptions> = {}
  ): Promise<DiscoveryResult> {
    return this.discoverChannels({
      query,
      countries: [country],
      minSubscribers,
      saveToDatabase: true,
      skipExisting: true,
      ...options,
    });
  }

  /**
   * Discover channels across multiple countries for a specific niche
   */
  async discoverNicheChannels(
    query: string,
    countries: YouTubeRegionCode[],
    minSubscribers: number = 5000,
    maxResults: number = 100
  ): Promise<DiscoveryResult> {
    return this.discoverChannels({
      query,
      countries,
      minSubscribers,
      maxResults,
      saveToDatabase: true,
      skipExisting: true,
    });
  }

  /**
   * Discover trending channels in specific languages
   */
  async discoverTrendingChannels(
    query: string,
    languages: YouTubeLanguageCode[],
    minSubscribers: number = 10000,
    maxResults: number = 50
  ): Promise<DiscoveryResult> {
    return this.discoverChannels({
      query,
      languages,
      minSubscribers,
      maxResults,
      saveToDatabase: true,
      skipExisting: true,
    });
  }

  /**
   * Get detailed information for channels and update database
   */
  async enrichChannelData(channelIds: string[]): Promise<void> {
    try {
      this.logger.info(`Enriching data for ${channelIds.length} channels`);

      const channelDetails = await this.youtubeService.getChannelsByIds(channelIds);

      for (const channel of channelDetails) {
        try {
          // Check if channel exists in database
          const existing = await this.influencerRepository.findByPlatformAndInfluencerId(
            Platform.YOUTUBE,
            channel.id
          );

          if (existing) {
            // Update existing record with enriched data
            // Note: This would require extending the influencer model to store additional data
            this.logger.info(`Updated channel data for: ${channel.snippet?.title}`);
          } else {
            // Create new record
            await this.influencerRepository.create({
              platform: Platform.YOUTUBE,
              influencer_id: channel.id,
            });
            this.logger.info(`Created new channel record for: ${channel.snippet?.title}`);
          }

        } catch (error) {
          this.logger.error(`Error enriching channel ${channel.id}:`, error);
        }
      }

    } catch (error) {
      this.logger.error('Error enriching channel data:', error);
      throw error;
    }
  }

  /**
   * Save discovered channels to database
   */
  private async saveChannelsToDatabase(
    channels: YouTubeChannelSearchResult[],
    skipExisting: boolean = true
  ): Promise<{ new: number; existing: number; saved: number; errors: string[] }> {
    const result = { new: 0, existing: 0, saved: 0, errors: [] as string[] };

    for (const channel of channels) {
      try {
        // Check if channel already exists
        const exists = await this.influencerRepository.exists(
          Platform.YOUTUBE,
          channel.channelId
        );

        if (exists) {
          result.existing++;
          if (skipExisting) {
            this.logger.debug(`Skipping existing channel: ${channel.title}`);
            continue;
          }
        } else {
          result.new++;
        }

        // Create influencer record
        const influencerData: CreateInfluencerData = {
          platform: Platform.YOUTUBE,
          influencer_id: channel.channelId,
        };

        if (!exists) {
          await this.influencerRepository.create(influencerData);
          result.saved++;
          this.logger.info(`Saved channel: ${channel.title} (${channel.subscriberCount?.toLocaleString()} subscribers)`);
        }

      } catch (error) {
        const errorMsg = `Error saving channel ${channel.title}: ${String(error)}`;
        this.logger.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }

    return result;
  }

  /**
   * Remove duplicate channels based on channel ID
   */
  private removeDuplicateChannels(channels: YouTubeChannelSearchResult[]): YouTubeChannelSearchResult[] {
    const seen = new Set<string>();
    return channels.filter(channel => {
      if (seen.has(channel.channelId)) {
        return false;
      }
      seen.add(channel.channelId);
      return true;
    });
  }

  /**
   * Get discovery statistics from database
   */
  async getDiscoveryStats(): Promise<{
    totalChannels: number;
    youtubeChannels: number;
    recentlyAdded: number;
  }> {
    try {
      const totalChannels = await this.influencerRepository.count();
      const youtubeChannels = await this.influencerRepository.count({
        platform: Platform.YOUTUBE,
      });

      // Get channels added in the last 24 hours
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      // Note: This would require adding a date filter to the repository
      // For now, we'll return 0 for recently added
      const recentlyAdded = 0;

      return {
        totalChannels,
        youtubeChannels,
        recentlyAdded,
      };

    } catch (error) {
      this.logger.error('Error getting discovery stats:', error);
      throw error;
    }
  }

  /**
   * Validate search options
   */
  private validateSearchOptions(options: DiscoverySearchOptions): void {
    if (!options.query || options.query.trim().length === 0) {
      throw new Error('Search query is required');
    }

    if (options.maxResults && (options.maxResults < 1 || options.maxResults > 500)) {
      throw new Error('maxResults must be between 1 and 500');
    }

    if (options.minSubscribers && options.minSubscribers < 0) {
      throw new Error('minSubscribers must be non-negative');
    }

    if (options.maxSubscribers && options.maxSubscribers < 0) {
      throw new Error('maxSubscribers must be non-negative');
    }

    if (options.minSubscribers && options.maxSubscribers && 
        options.minSubscribers > options.maxSubscribers) {
      throw new Error('minSubscribers cannot be greater than maxSubscribers');
    }
  }
}

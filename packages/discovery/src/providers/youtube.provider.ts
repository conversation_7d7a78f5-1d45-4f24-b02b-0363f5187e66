import {google, youtube_v3} from "googleapis";
import {ConfigLoader} from "../common/config-loader";
import {Logger} from '../common/logger';

export class YoutubeProvider {
    private readonly youtubeClient: youtube_v3.Youtube;
    private readonly logger = Logger.c(YoutubeProvider.name);

    constructor() {
        this.youtubeClient = google.youtube({
            version: 'v3',
            auth: ConfigLoader.load().providers.youtube.apiKey,
        });
    }

    async search() {
        const response = await this.youtubeClient.search.list({
            part: ['snippet'],
            q: 'popular',
            type: ['channel'],
            regionCode: 'SI',
            maxResults: 50
        });

        this.logger.info(`Response ${JSON.stringify(response.data)}`);

        return response.data;
    }
}
import {google, youtube_v3} from "googleapis";
import {ConfigLoader} from "../common/config-loader";
import {Logger} from '../common/logger';

export class YoutubeProvider {
    private readonly youtubeClient: youtube_v3.Youtube;
    private readonly logger = Logger.c(YoutubeProvider.name);

    constructor() {
        this.youtubeClient = google.youtube({
            version: 'v3',
            auth: ConfigLoader.load().providers.youtube.apiKey,
        });
    }

    async search() {
        const response = await this.youtubeClient.search.list({
            part: ['snippet'],
            q: 'slovenija',
            type: ['channel'],
            regionCode: 'SI',
            maxResults: 150
        });

        return response.data;
    }

    async getChannelDetails(channelId: string) {
        const response = await this.youtubeClient.channels.list({
            part: ['snippet', 'statistics', 'contentDetails', 'topicDetails', 'status', 'brandingSettings', 'localizations', 'contentOwnerDetails'/*, 'auditDetails', 'contentOwnerDetails'*/],
            id: [channelId]
        });

        console.log('Channel details', JSON.stringify(response.data));

        return response.data;
    }

    async getVideoDetails(videoId: string) {
        const response = await this.youtubeClient.videos.list({
            part: ['snippet', 'statistics', 'contentDetails', 'topicDetails', 'status', 'localizations'],
            id: [videoId]
        });

        console.log('Video details', JSON.stringify(response.data));

        return response.data;
    }
}
import { <PERSON><PERSON> } from 'knex';
import { Logger } from '../logger/logger';
import {
  Influencer,
  CreateInfluencerData,
  UpdateInfluencerData,
  InfluencerFilters,
  InfluencerWithPlatforms,
} from './influencer.model';
import {DatabaseService} from "@/database/database.service";

export class InfluencerRepository {
  private readonly tableName = 'influencers';
  private readonly knex: Knex;
  private readonly logger: Logger = Logger.c(InfluencerRepository.name);

  /**
   * Create a new influencer record
   */
  async create(data: CreateInfluencerData): Promise<Influencer> {
    try {
      this.logger.debug('Creating new influencer:', data);
      
      const [influencer] = await DatabaseService.getInstance().k(this.tableName)
        .insert(data)
        .returning('*');

      this.logger.info(`Created influencer with ID: ${influencer.id} - ${influencer.display_name}`);
      return influencer;
    } catch (error) {
      this.logger.error('Failed to create influencer:', error);
      throw error;
    }
  }

  /**
   * Create multiple influencer records in a batch
   */
  async createMany(data: CreateInfluencerData[]): Promise<Influencer[]> {
    try {
      this.logger.debug(`Creating ${data.length} influencers in batch`);
      
      const influencers = await DatabaseService.getInstance().k(this.tableName)
        .insert(data)
        .returning('*');

      this.logger.info(`Created ${influencers.length} influencers in batch`);
      return influencers;
    } catch (error) {
      this.logger.error('Failed to create influencers in batch:', error);
      throw error;
    }
  }

  /**
   * Find an influencer by ID
   */
  async findById(id: number): Promise<Influencer | null> {
    try {
      const influencer = await DatabaseService.getInstance().k(this.tableName)
        .where('id', id)
        .first();

      return influencer || null;
    } catch (error) {
      this.logger.error(`Failed to find influencer by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find an influencer by display name
   */
  async findByDisplayName(displayName: string): Promise<Influencer | null> {
    try {
      const influencer = await DatabaseService.getInstance().k(this.tableName)
        .where('display_name', displayName)
        .first();

      return influencer || null;
    } catch (error) {
      this.logger.error(`Failed to find influencer by display name ${displayName}:`, error);
      throw error;
    }
  }

  /**
   * Find influencers with optional filters
   */
  async find(filters: InfluencerFilters = {}): Promise<Influencer[]> {
    try {
      let query = DatabaseService.getInstance().k(this.tableName);

      // Apply filters
      if (filters.id) {
        query = query.where('id', filters.id);
      }

      if (filters.display_name) {
        query = query.where('display_name', 'ilike', `%${filters.display_name}%`);
      }

      if (filters.language) {
        query = query.where('language', filters.language);
      }

      if (filters.country) {
        query = query.where('country', filters.country);
      }

      if (filters.languages && filters.languages.length > 0) {
        query = query.whereIn('language', filters.languages);
      }

      if (filters.countries && filters.countries.length > 0) {
        query = query.whereIn('country', filters.countries);
      }

      // Apply ordering
      const orderBy = filters.orderBy || 'created_at';
      const orderDirection = filters.orderDirection || 'desc';
      query = query.orderBy(orderBy, orderDirection);

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      const influencers = await query;
      return influencers;
    } catch (error) {
      this.logger.error('Failed to find influencers:', error);
      throw error;
    }
  }

  /**
   * Find influencers by country
   */
  async findByCountry(country: string, limit?: number, offset?: number): Promise<Influencer[]> {
    return this.find({ country, limit, offset });
  }

  /**
   * Find influencers by language
   */
  async findByLanguage(language: string, limit?: number, offset?: number): Promise<Influencer[]> {
    return this.find({ language, limit, offset });
  }

  /**
   * Find influencer with their platform data
   */
  async findByIdWithPlatforms(id: number): Promise<InfluencerWithPlatforms | null> {
    try {
      const influencer = await this.findById(id);
      if (!influencer) {
        return null;
      }

      const platforms = await DatabaseService.getInstance().k('influencer_platform')
        .where('influencer_id', id)
        .orderBy('created_at', 'desc');

      return {
        ...influencer,
        platforms,
      };
    } catch (error) {
      this.logger.error(`Failed to find influencer with platforms by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find influencers with their platform data
   */
  async findWithPlatforms(filters: InfluencerFilters = {}): Promise<InfluencerWithPlatforms[]> {
    try {
      const influencers = await this.find(filters);
      
      if (influencers.length === 0) {
        return [];
      }

      const influencerIds = influencers.map(inf => inf.id!);
      const platforms = await DatabaseService.getInstance().k('influencer_platform')
        .whereIn('influencer_id', influencerIds)
        .orderBy('created_at', 'desc');

      // Group platforms by influencer_id
      const platformsByInfluencer = platforms.reduce((acc, platform) => {
        if (!acc[platform.influencer_id]) {
          acc[platform.influencer_id] = [];
        }
        acc[platform.influencer_id].push(platform);
        return acc;
      }, {} as Record<number, any[]>);

      // Attach platforms to influencers
      return influencers.map(influencer => ({
        ...influencer,
        platforms: platformsByInfluencer[influencer.id!] || [],
      }));
    } catch (error) {
      this.logger.error('Failed to find influencers with platforms:', error);
      throw error;
    }
  }

  /**
   * Check if an influencer exists by display name
   */
  async existsByDisplayName(displayName: string): Promise<boolean> {
    try {
      const count = await DatabaseService.getInstance().k(this.tableName)
        .where('display_name', displayName)
        .count('id as count')
        .first();

      return parseInt(count?.count as string) > 0;
    } catch (error) {
      this.logger.error(`Failed to check if influencer exists:`, error);
      throw error;
    }
  }

  /**
   * Get total count of influencers with optional filters
   */
  async count(filters: InfluencerFilters = {}): Promise<number> {
    try {
      let query = DatabaseService.getInstance().k(this.tableName);

      // Apply filters (same as find method but without pagination and ordering)
      if (filters.language) {
        query = query.where('language', filters.language);
      }

      if (filters.country) {
        query = query.where('country', filters.country);
      }

      if (filters.languages && filters.languages.length > 0) {
        query = query.whereIn('language', filters.languages);
      }

      if (filters.countries && filters.countries.length > 0) {
        query = query.whereIn('country', filters.countries);
      }

      if (filters.display_name) {
        query = query.where('display_name', 'ilike', `%${filters.display_name}%`);
      }

      const result = await query.count('id as count').first();
      return parseInt(result?.count as string) || 0;
    } catch (error) {
      this.logger.error('Failed to count influencers:', error);
      throw error;
    }
  }

  /**
   * Update an influencer record
   */
  async update(id: number, data: UpdateInfluencerData): Promise<Influencer | null> {
    try {
      this.logger.debug(`Updating influencer ${id}:`, data);
      
      const [influencer] = await DatabaseService.getInstance().k(this.tableName)
        .where('id', id)
        .update({
          ...data,
          updated_at: new Date(),
        })
        .returning('*');

      if (influencer) {
        this.logger.info(`Updated influencer with ID: ${influencer.id} - ${influencer.display_name}`);
      }

      return influencer || null;
    } catch (error) {
      this.logger.error(`Failed to update influencer ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an influencer record (and cascade to platform records)
   */
  async delete(id: number): Promise<boolean> {
    try {
      this.logger.debug(`Deleting influencer ${id}`);
      
      const deletedCount = await DatabaseService.getInstance().k(this.tableName)
        .where('id', id)
        .del();

      const success = deletedCount > 0;
      if (success) {
        this.logger.info(`Deleted influencer with ID: ${id}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Failed to delete influencer ${id}:`, error);
      throw error;
    }
  }

  /**
   * Search influencers by display name (fuzzy search)
   */
  async search(searchTerm: string, limit: number = 50): Promise<Influencer[]> {
    try {
      const influencers = await DatabaseService.getInstance().k(this.tableName)
        .where('display_name', 'ilike', `%${searchTerm}%`)
        .orderBy('display_name', 'asc')
        .limit(limit);

      return influencers;
    } catch (error) {
      this.logger.error(`Failed to search influencers with term "${searchTerm}":`, error);
      throw error;
    }
  }

  /**
   * Get influencers statistics by country
   */
  async getStatsByCountry(): Promise<Array<{ country: string; count: number }>> {
    try {
      const stats = await DatabaseService.getInstance().k(this.tableName)
        .select('country')
        .count('id as count')
        .whereNotNull('country')
        .groupBy('country')
        .orderBy('count', 'desc');

      return stats.map(stat => ({
        country: stat.country,
        count: parseInt(stat.count as string),
      }));
    } catch (error) {
      this.logger.error('Failed to get influencer stats by country:', error);
      throw error;
    }
  }

  /**
   * Get influencers statistics by language
   */
  async getStatsByLanguage(): Promise<Array<{ language: string; count: number }>> {
    try {
      const stats = await DatabaseService.getInstance().k(this.tableName)
        .select('language')
        .count('id as count')
        .whereNotNull('language')
        .groupBy('language')
        .orderBy('count', 'desc');

      return stats.map(stat => ({
        language: stat.language,
        count: parseInt(stat.count as string),
      }));
    } catch (error) {
      this.logger.error('Failed to get influencer stats by language:', error);
      throw error;
    }
  }
}

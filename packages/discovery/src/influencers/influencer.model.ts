/**
 * Influencer model representing the main influencer entity
 */
export interface Influencer {
  id?: number;
  display_name: string;
  language?: string;
  country?: string;
  created_at?: Date;
  updated_at?: Date;
}

/**
 * Data required to create a new influencer record
 */
export interface CreateInfluencerData {
  display_name: string;
  language?: string;
  country?: string;
}

/**
 * Data for updating an influencer record
 */
export interface UpdateInfluencerData {
  display_name?: string;
  language?: string;
  country?: string;
}

/**
 * Query filters for finding influencers
 */
export interface InfluencerFilters {
  id?: number;
  display_name?: string;
  language?: string;
  country?: string;
  languages?: string[];
  countries?: string[];
  limit?: number;
  offset?: number;
  orderBy?: 'id' | 'display_name' | 'created_at' | 'updated_at';
  orderDirection?: 'asc' | 'desc';
}

/**
 * Influencer with platform data included
 */
export interface InfluencerWithPlatforms extends Influencer {
  platforms?: InfluencerPlatform[];
}

/**
 * Platform-specific influencer data
 */
export interface InfluencerPlatform {
  id?: number;
  influencer_id: number;
  platform: string;
  platform_id: string;
  followers?: number;
  language?: string;
  joined_platform?: Date;
  media_count?: number;
  number_of_views?: number;
  created_at?: Date;
  updated_at?: Date;
}

/**
 * Data required to create a new influencer platform record
 */
export interface CreateInfluencerPlatformData {
  influencer_id: number;
  platform: string;
  platform_id: string;
  followers?: number;
  language?: string;
  joined_platform?: Date | string;
  media_count?: number;
  number_of_views?: number;
}

/**
 * Data for updating an influencer platform record
 */
export interface UpdateInfluencerPlatformData {
  followers?: number;
  language?: string;
  joined_platform?: Date | string;
  media_count?: number;
  number_of_views?: number;
}

/**
 * Query filters for finding influencer platforms
 */
export interface InfluencerPlatformFilters {
  id?: number;
  influencer_id?: number;
  platform?: string;
  platform_id?: string;
  platforms?: string[];
  influencer_ids?: number[];
  minFollowers?: number;
  maxFollowers?: number;
  language?: string;
  languages?: string[];
  limit?: number;
  offset?: number;
  orderBy?: 'id' | 'followers' | 'media_count' | 'number_of_views' | 'created_at' | 'updated_at';
  orderDirection?: 'asc' | 'desc';
}

/**
 * Supported platforms enum
 */
export enum Platform {
  YOUTUBE = 'youtube',
  INSTAGRAM = 'instagram',
  TIKTOK = 'tiktok',
  TWITTER = 'twitter',
  FACEBOOK = 'facebook',
  TWITCH = 'twitch',
  LINKEDIN = 'linkedin',
  SNAPCHAT = 'snapchat',
  PINTEREST = 'pinterest',
  REDDIT = 'reddit',
}

/**
 * Country codes (ISO 3166-1 alpha-3)
 */
export enum CountryCode {
  USA = 'USA', // United States
  GBR = 'GBR', // United Kingdom
  CAN = 'CAN', // Canada
  AUS = 'AUS', // Australia
  DEU = 'DEU', // Germany
  FRA = 'FRA', // France
  ESP = 'ESP', // Spain
  ITA = 'ITA', // Italy
  NLD = 'NLD', // Netherlands
  SWE = 'SWE', // Sweden
  NOR = 'NOR', // Norway
  DNK = 'DNK', // Denmark
  FIN = 'FIN', // Finland
  POL = 'POL', // Poland
  CZE = 'CZE', // Czech Republic
  HUN = 'HUN', // Hungary
  ROU = 'ROU', // Romania
  BGR = 'BGR', // Bulgaria
  HRV = 'HRV', // Croatia
  SVN = 'SVN', // Slovenia
  SVK = 'SVK', // Slovakia
  LTU = 'LTU', // Lithuania
  LVA = 'LVA', // Latvia
  EST = 'EST', // Estonia
  BRA = 'BRA', // Brazil
  MEX = 'MEX', // Mexico
  ARG = 'ARG', // Argentina
  CHL = 'CHL', // Chile
  COL = 'COL', // Colombia
  PER = 'PER', // Peru
  VEN = 'VEN', // Venezuela
  JPN = 'JPN', // Japan
  KOR = 'KOR', // South Korea
  CHN = 'CHN', // China
  IND = 'IND', // India
  IDN = 'IDN', // Indonesia
  THA = 'THA', // Thailand
  VNM = 'VNM', // Vietnam
  PHL = 'PHL', // Philippines
  MYS = 'MYS', // Malaysia
  SGP = 'SGP', // Singapore
  TWN = 'TWN', // Taiwan
  HKG = 'HKG', // Hong Kong
  RUS = 'RUS', // Russia
  UKR = 'UKR', // Ukraine
  TUR = 'TUR', // Turkey
  SAU = 'SAU', // Saudi Arabia
  ARE = 'ARE', // United Arab Emirates
  ISR = 'ISR', // Israel
  EGY = 'EGY', // Egypt
  ZAF = 'ZAF', // South Africa
  NGA = 'NGA', // Nigeria
  KEN = 'KEN', // Kenya
  GHA = 'GHA', // Ghana
}

/**
 * Language codes (ISO 639-1)
 */
export enum LanguageCode {
  EN = 'en', // English
  ES = 'es', // Spanish
  FR = 'fr', // French
  DE = 'de', // German
  IT = 'it', // Italian
  PT = 'pt', // Portuguese
  RU = 'ru', // Russian
  JA = 'ja', // Japanese
  KO = 'ko', // Korean
  ZH = 'zh', // Chinese
  AR = 'ar', // Arabic
  HI = 'hi', // Hindi
  BN = 'bn', // Bengali
  UR = 'ur', // Urdu
  TR = 'tr', // Turkish
  PL = 'pl', // Polish
  NL = 'nl', // Dutch
  SV = 'sv', // Swedish
  NO = 'no', // Norwegian
  DA = 'da', // Danish
  FI = 'fi', // Finnish
  CS = 'cs', // Czech
  HU = 'hu', // Hungarian
  RO = 'ro', // Romanian
  BG = 'bg', // Bulgarian
  HR = 'hr', // Croatian
  SK = 'sk', // Slovak
  SL = 'sl', // Slovenian
  LT = 'lt', // Lithuanian
  LV = 'lv', // Latvian
  ET = 'et', // Estonian
  TH = 'th', // Thai
  VI = 'vi', // Vietnamese
  ID = 'id', // Indonesian
  MS = 'ms', // Malay
  TL = 'tl', // Filipino
  HE = 'he', // Hebrew
  FA = 'fa', // Persian
  SW = 'sw', // Swahili
  AM = 'am', // Amharic
}

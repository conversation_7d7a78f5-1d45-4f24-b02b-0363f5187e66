import { <PERSON><PERSON> } from 'knex';
import { Logger } from '../logger/logger';
import {
  InfluencerPlatform,
  CreateInfluencerPlatformData,
  UpdateInfluencerPlatformData,
  InfluencerPlatformFilters,
} from './influencer.model';
import {DatabaseService} from "@/database/database.service";

export class InfluencerPlatformRepository {
  private readonly tableName = 'influencer_platform';
  private readonly knex: Knex;
  private readonly logger: Logger = Logger.c(InfluencerPlatformRepository.name);

  /**
   * Create a new influencer platform record
   */
  async create(data: CreateInfluencerPlatformData): Promise<InfluencerPlatform> {
    try {
      this.logger.debug('Creating new influencer platform:', data);
      
      // Convert joined_platform to Date if it's a string
      const processedData = {
        ...data,
        joined_platform: data.joined_platform ? 
          (typeof data.joined_platform === 'string' ? new Date(data.joined_platform) : data.joined_platform) 
          : undefined,
      };
      
      const [influencerPlatform] = await DatabaseService.getInstance().k(this.tableName)
        .insert(processedData)
        .returning('*');

      this.logger.info(`Created influencer platform with ID: ${influencerPlatform.id} - ${data.platform}/${data.platform_id}`);
      return influencerPlatform;
    } catch (error) {
      this.logger.error('Failed to create influencer platform:', error);
      throw error;
    }
  }

  /**
   * Create multiple influencer platform records in a batch
   */
  async createMany(data: CreateInfluencerPlatformData[]): Promise<InfluencerPlatform[]> {
    try {
      this.logger.debug(`Creating ${data.length} influencer platforms in batch`);
      
      // Process all data items
      const processedData = data.map(item => ({
        ...item,
        joined_platform: item.joined_platform ? 
          (typeof item.joined_platform === 'string' ? new Date(item.joined_platform) : item.joined_platform) 
          : undefined,
      }));
      
      const influencerPlatforms = await DatabaseService.getInstance().k(this.tableName)
        .insert(processedData)
        .returning('*');

      this.logger.info(`Created ${influencerPlatforms.length} influencer platforms in batch`);
      return influencerPlatforms;
    } catch (error) {
      this.logger.error('Failed to create influencer platforms in batch:', error);
      throw error;
    }
  }

  /**
   * Find an influencer platform by ID
   */
  async findById(id: number): Promise<InfluencerPlatform | null> {
    try {
      const influencerPlatform = await DatabaseService.getInstance().k(this.tableName)
        .where('id', id)
        .first();

      return influencerPlatform || null;
    } catch (error) {
      this.logger.error(`Failed to find influencer platform by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find an influencer platform by influencer ID and platform
   */
  async findByInfluencerAndPlatform(influencerId: number, platform: string): Promise<InfluencerPlatform | null> {
    try {
      const influencerPlatform = await DatabaseService.getInstance().k(this.tableName)
        .where('influencer_id', influencerId)
        .where('platform', platform)
        .first();

      return influencerPlatform || null;
    } catch (error) {
      this.logger.error(`Failed to find influencer platform by influencer ${influencerId} and platform ${platform}:`, error);
      throw error;
    }
  }

  /**
   * Find an influencer platform by platform and platform ID
   */
  async findByPlatformAndPlatformId(platform: string, platformId: string): Promise<InfluencerPlatform | null> {
    try {
      const influencerPlatform = await DatabaseService.getInstance().k(this.tableName)
        .where('platform', platform)
        .where('platform_id', platformId)
        .first();

      return influencerPlatform || null;
    } catch (error) {
      this.logger.error(`Failed to find influencer platform by platform ${platform} and platform ID ${platformId}:`, error);
      throw error;
    }
  }

  /**
   * Find influencer platforms with optional filters
   */
  async find(filters: InfluencerPlatformFilters = {}): Promise<InfluencerPlatform[]> {
    try {
      let query = DatabaseService.getInstance().k(this.tableName);

      // Apply filters
      if (filters.id) {
        query = query.where('id', filters.id);
      }

      if (filters.influencer_id) {
        query = query.where('influencer_id', filters.influencer_id);
      }

      if (filters.platform) {
        query = query.where('platform', filters.platform);
      }

      if (filters.platform_id) {
        query = query.where('platform_id', filters.platform_id);
      }

      if (filters.platforms && filters.platforms.length > 0) {
        query = query.whereIn('platform', filters.platforms);
      }

      if (filters.influencer_ids && filters.influencer_ids.length > 0) {
        query = query.whereIn('influencer_id', filters.influencer_ids);
      }

      if (filters.minFollowers) {
        query = query.where('followers', '>=', filters.minFollowers);
      }

      if (filters.maxFollowers) {
        query = query.where('followers', '<=', filters.maxFollowers);
      }

      if (filters.language) {
        query = query.where('language', filters.language);
      }

      if (filters.languages && filters.languages.length > 0) {
        query = query.whereIn('language', filters.languages);
      }

      // Apply ordering
      const orderBy = filters.orderBy || 'created_at';
      const orderDirection = filters.orderDirection || 'desc';
      query = query.orderBy(orderBy, orderDirection);

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      const influencerPlatforms = await query;
      return influencerPlatforms;
    } catch (error) {
      this.logger.error('Failed to find influencer platforms:', error);
      throw error;
    }
  }

  /**
   * Find all platforms for a specific influencer
   */
  async findByInfluencerId(influencerId: number): Promise<InfluencerPlatform[]> {
    return this.find({ influencer_id: influencerId });
  }

  /**
   * Find all influencer platforms for a specific platform
   */
  async findByPlatform(platform: string, limit?: number, offset?: number): Promise<InfluencerPlatform[]> {
    return this.find({ platform, limit, offset });
  }

  /**
   * Find top influencers by followers on a platform
   */
  async findTopByFollowers(platform: string, limit: number = 50): Promise<InfluencerPlatform[]> {
    return this.find({
      platform,
      limit,
      orderBy: 'followers',
      orderDirection: 'desc',
    });
  }

  /**
   * Find influencers with minimum follower count
   */
  async findWithMinFollowers(minFollowers: number, platform?: string, limit?: number): Promise<InfluencerPlatform[]> {
    return this.find({
      platform,
      minFollowers,
      limit,
      orderBy: 'followers',
      orderDirection: 'desc',
    });
  }

  /**
   * Check if an influencer platform exists
   */
  async exists(influencerId: number, platform: string): Promise<boolean> {
    try {
      const count = await DatabaseService.getInstance().k(this.tableName)
        .where('influencer_id', influencerId)
        .where('platform', platform)
        .count('id as count')
        .first();

      return parseInt(count?.count as string) > 0;
    } catch (error) {
      this.logger.error(`Failed to check if influencer platform exists:`, error);
      throw error;
    }
  }

  /**
   * Check if a platform ID exists for a platform
   */
  async existsByPlatformId(platform: string, platformId: string): Promise<boolean> {
    try {
      const count = await DatabaseService.getInstance().k(this.tableName)
        .where('platform', platform)
        .where('platform_id', platformId)
        .count('id as count')
        .first();

      return parseInt(count?.count as string) > 0;
    } catch (error) {
      this.logger.error(`Failed to check if platform ID exists:`, error);
      throw error;
    }
  }

  /**
   * Get total count of influencer platforms with optional filters
   */
  async count(filters: InfluencerPlatformFilters = {}): Promise<number> {
    try {
      let query = DatabaseService.getInstance().k(this.tableName);

      // Apply filters (same as find method but without pagination and ordering)
      if (filters.influencer_id) {
        query = query.where('influencer_id', filters.influencer_id);
      }

      if (filters.platform) {
        query = query.where('platform', filters.platform);
      }

      if (filters.platforms && filters.platforms.length > 0) {
        query = query.whereIn('platform', filters.platforms);
      }

      if (filters.minFollowers) {
        query = query.where('followers', '>=', filters.minFollowers);
      }

      if (filters.maxFollowers) {
        query = query.where('followers', '<=', filters.maxFollowers);
      }

      if (filters.language) {
        query = query.where('language', filters.language);
      }

      if (filters.languages && filters.languages.length > 0) {
        query = query.whereIn('language', filters.languages);
      }

      const result = await query.count('id as count').first();
      return parseInt(result?.count as string) || 0;
    } catch (error) {
      this.logger.error('Failed to count influencer platforms:', error);
      throw error;
    }
  }

  /**
   * Update an influencer platform record
   */
  async update(id: number, data: UpdateInfluencerPlatformData): Promise<InfluencerPlatform | null> {
    try {
      this.logger.debug(`Updating influencer platform ${id}:`, data);
      
      // Convert joined_platform to Date if it's a string
      const processedData = {
        ...data,
        joined_platform: data.joined_platform ? 
          (typeof data.joined_platform === 'string' ? new Date(data.joined_platform) : data.joined_platform) 
          : undefined,
        updated_at: new Date(),
      };
      
      const [influencerPlatform] = await DatabaseService.getInstance().k(this.tableName)
        .where('id', id)
        .update(processedData)
        .returning('*');

      if (influencerPlatform) {
        this.logger.info(`Updated influencer platform with ID: ${influencerPlatform.id}`);
      }

      return influencerPlatform || null;
    } catch (error) {
      this.logger.error(`Failed to update influencer platform ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update follower count for an influencer platform
   */
  async updateFollowers(id: number, followers: number): Promise<InfluencerPlatform | null> {
    return this.update(id, { followers });
  }

  /**
   * Update media count for an influencer platform
   */
  async updateMediaCount(id: number, mediaCount: number): Promise<InfluencerPlatform | null> {
    return this.update(id, { media_count: mediaCount });
  }

  /**
   * Update view count for an influencer platform
   */
  async updateViewCount(id: number, viewCount: number): Promise<InfluencerPlatform | null> {
    return this.update(id, { number_of_views: viewCount });
  }

  /**
   * Delete an influencer platform record
   */
  async delete(id: number): Promise<boolean> {
    try {
      this.logger.debug(`Deleting influencer platform ${id}`);
      
      const deletedCount = await DatabaseService.getInstance().k(this.tableName)
        .where('id', id)
        .del();

      const success = deletedCount > 0;
      if (success) {
        this.logger.info(`Deleted influencer platform with ID: ${id}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Failed to delete influencer platform ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete all platforms for an influencer
   */
  async deleteByInfluencerId(influencerId: number): Promise<number> {
    try {
      this.logger.debug(`Deleting all platforms for influencer ${influencerId}`);
      
      const deletedCount = await DatabaseService.getInstance().k(this.tableName)
        .where('influencer_id', influencerId)
        .del();

      this.logger.info(`Deleted ${deletedCount} platforms for influencer ${influencerId}`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Failed to delete platforms for influencer ${influencerId}:`, error);
      throw error;
    }
  }

  /**
   * Get platform statistics
   */
  async getStatsByPlatform(): Promise<Array<{ platform: string; count: number; totalFollowers: number }>> {
    try {
      const stats = await DatabaseService.getInstance().k(this.tableName)
        .select('platform')
        .count('id as count')
        .sum('followers as totalFollowers')
        .groupBy('platform')
        .orderBy('count', 'desc');

      return stats.map(stat => ({
        platform: stat.platform,
        count: parseInt(stat.count as string),
        totalFollowers: parseInt(stat.totalFollowers as string) || 0,
      }));
    } catch (error) {
      this.logger.error('Failed to get platform statistics:', error);
      throw error;
    }
  }

  /**
   * Get top influencers across all platforms by followers
   */
  async getTopInfluencersGlobal(limit: number = 100): Promise<InfluencerPlatform[]> {
    try {
      const topInfluencers = await DatabaseService.getInstance().k(this.tableName)
        .whereNotNull('followers')
        .orderBy('followers', 'desc')
        .limit(limit);

      return topInfluencers;
    } catch (error) {
      this.logger.error('Failed to get top influencers globally:', error);
      throw error;
    }
  }
}

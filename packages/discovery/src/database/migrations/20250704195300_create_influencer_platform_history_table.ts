import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('influencer_platform', (table) => {
    table.increments('id').primary().comment('Sequential auto-increment ID');
    table.integer('influencer_id').unsigned().notNullable().comment('Foreign key to influencers table');
    table.string('platform', 50).notNullable().comment('Platform name (youtube, instagram, tiktok, etc.)');
    table.string('platform_id', 255).notNullable().comment('User ID from the platform (e.g., YouTube channel ID, Instagram user ID)');
    table.bigInteger('followers').nullable().comment('Number of followers/subscribers');
    table.string('language', 10).nullable().comment('Language used on this platform');
    table.date('joined_platform').nullable().comment('Date when influencer joined the platform');
    table.integer('media_count').nullable().comment('Number of videos/posts on the platform');
    table.bigInteger('number_of_views').nullable().comment('Total number of views across all content');
    table.timestamps(true, true);

    // Foreign key constraint
    table.foreign('influencer_id').references('id').inTable('influencers').onDelete('CASCADE');

    // Unique constraint to prevent duplicate platform entries for same influencer
    table.unique(['influencer_id', 'platform'], 'influencer_platform_unique');

    // Indexes for common queries
    table.index('influencer_id', 'influencer_platform_influencer_id_index');
    table.index('platform', 'influencer_platform_platform_index');
    table.index('followers', 'influencer_platform_followers_index');
    table.index('language', 'influencer_platform_language_index');
    table.index(['platform', 'followers'], 'influencer_platform_platform_followers_index');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('influencer_platform');
}


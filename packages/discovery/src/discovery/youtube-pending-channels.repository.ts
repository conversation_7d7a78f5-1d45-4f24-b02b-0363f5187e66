import { DatabaseService } from '../database/database.service';
import { Logger } from '../common/logger';

export interface YoutubePendingChannel {
  id: string;
  discovery?: string;
  discovered_at: Date;
  created_at?: Date;
  title?: string;
  description?: string;
  image?: string;
  status: 'created' | 'processing' | 'processed';
  processing_at?: Date;
}

export interface CreateYoutubePendingChannelData {
  id: string;
  discovery?: string;
  created_at?: Date;
  title?: string;
  description?: string;
  image?: string;
}

export class YoutubePendingChannelsRepository {
  private static instance: YoutubePendingChannelsRepository;
  
  private readonly logger = Logger.c(YoutubePendingChannelsRepository.name);
  private readonly db: DatabaseService;

  constructor() {
    this.db = DatabaseService.getInstance();
  }
  
  public static getInstance(): YoutubePendingChannelsRepository {
    if (!YoutubePendingChannelsRepository.instance) {
      YoutubePendingChannelsRepository.instance = new YoutubePendingChannelsRepository();
    }
    return YoutubePendingChannelsRepository.instance;
  }

  /**
   * Insert channel data, ignore if already exists
   */
  async insertChannel(data: CreateYoutubePendingChannelData): Promise<boolean> {
    try {
      const result = await this.db.k()('youtube_pending_channels')
        .insert({
          id: data.id,
          discovery: data.discovery,
          created_at: data.created_at,
          title: data.title,
          description: data.description,
          image: data.image,
          status: 'created'
        })
        .onConflict('id')
        .ignore();
      
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error(`Error inserting channel ${data.id}:`, error);
      throw error;
    }
  }

  /**
   * Get channels that need processing (created status or stale processing)
   */
  async getChannelsToProcess(staleHours: number = 2, limit: number = 100): Promise<YoutubePendingChannel[]> {
    try {
      const staleTime = new Date();
      staleTime.setHours(staleTime.getHours() - staleHours);

      const channels = await this.db.k()('youtube_pending_channels')
        .where('status', 'created')
        .orWhere(function() {
          this.where('status', 'processing')
              .andWhere('processing_at', '<', staleTime);
        })
        .orderBy('discovered_at', 'asc')
        .limit(limit);

      this.logger.info(`Found ${channels.length} channels to process`);
      return channels;
    } catch (error) {
      this.logger.error('Error getting channels to process:', error);
      throw error;
    }
  }
}
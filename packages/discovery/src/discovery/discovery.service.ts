import {Runnable} from "../common/runnable";
import {YoutubeProvider} from "../providers/youtube.provider";
import {Logger} from "../common/logger";

export class DiscoveryService implements Runnable {
    private readonly logger = Logger.c(DiscoveryService.name);
    private static instance: DiscoveryService;

    public static getInstance(): DiscoveryService {
        if (!DiscoveryService.instance) {
            DiscoveryService.instance = new DiscoveryService();
        }
        return DiscoveryService.instance;
    }

    async start(): Promise<void> {
        const provider = new YoutubeProvider();
        const results = await provider.search();

        this.logger.info(`Found ${results.pageInfo?.totalResults} results`);

        if (results.items) {
            for (const item of results.items) {
                if (item.id.kind === 'youtube#channel') {
                    const channel = item.snippet;
                    const data: CreateYoutubePendingChannelData = {
                        id: channel.channelId,
                        discovery: 'search',
                        created_at: channel.publishedAt,
                        title: channel.title,
                        description: channel.description,
                        image: channel.thumbnails.medium.url,
                    };

                    const inserted = await YoutubePendingChannelsRepository.getInstance().insertChannel(data);

                    if (inserted) {
                        this.logger.info(`Inserted channel: ${channel.title} - ${channel.channelId}`);
                    }
                }
            }
        }
    }

    async stop(): Promise<void> {
        return Promise.resolve();
    }
}
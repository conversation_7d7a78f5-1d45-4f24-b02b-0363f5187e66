import {Runnable} from "../common/runnable";
import {YoutubeProvider} from "../providers/youtube.provider";
import {Logger} from "../common/logger";
import {CreateYoutubePendingChannelData, YoutubePendingChannelsRepository} from "./youtube-pending-channels.repository";
import {InfluencerRepository, Platform, InfluencerPlatformRepository} from "../influencers";
import {LanguageDetector} from "./language-detector";
import {YouTubeImportedVideo, YouTubeImportedVideoRepository} from "../import";

export class DiscoveryService implements Runnable {
    private readonly logger = Logger.c(DiscoveryService.name);
    private static instance: DiscoveryService;

    public static getInstance(): DiscoveryService {
        if (!DiscoveryService.instance) {
            DiscoveryService.instance = new DiscoveryService();
        }
        return DiscoveryService.instance;
    }

    async start(): Promise<void> {
        const provider = new YoutubeProvider();
        // let nextPageToken: string | null = null;
        //
        // do {
        //     nextPageToken = await this.findChannels(provider, nextPageToken);
        //
        //     const pendingChannels = await YoutubePendingChannelsRepository.getInstance().getChannelsToProcess();
        //
        //     for (const channel of pendingChannels) {
        //         this.logger.info(`Processing channel: ${channel.id}`);
        //
        //         await YoutubePendingChannelsRepository.getInstance().updateChannelStatus(channel.id, 'processing');
        //         await this.retrieveChannelDetails(channel.id, provider);
        //         await YoutubePendingChannelsRepository.getInstance().updateChannelStatus(channel.id, 'processed');
        //
        //         new Promise(resolve => setTimeout(resolve, 200));
        //     }
        // } while (nextPageToken);

        // await provider.getVideoDetails('22acl4SwlPg');

        // await this.importPendingVideos();

        await this.importChannels();
    }

    async stop(): Promise<void> {
        return Promise.resolve();
    }

    async findChannels(provider: YoutubeProvider, pageToken: string | null = null): Promise<string | null> {
        const results = await provider.search();

        this.logger.info(`Found ${results.pageInfo?.totalResults} results`);

        if (results.items) {
            for (const item of results.items) {
                if (item.id?.kind === 'youtube#channel' && item.snippet) {
                    const channel = item.snippet;
                    const data: CreateYoutubePendingChannelData = {
                        id: channel.channelId || '',
                        discovery: 'search',
                        created_at: channel.publishedAt ? new Date(channel.publishedAt) : undefined,
                        title: channel.title || undefined,
                        description: channel.description || undefined,
                        image: channel.thumbnails?.medium?.url || undefined,
                    };

                    const inserted = await YoutubePendingChannelsRepository.getInstance().insertChannel(data);

                    if (inserted) {
                        this.logger.info(`Inserted channel: ${channel.title || 'Unknown'} - ${channel.channelId || 'Unknown'}`);
                    }
                }
            }
        }

        return results.nextPageToken || null;
    }

    async retrieveChannelDetails(channelId: string, provider: YoutubeProvider): Promise<void> {
        const channel = await provider.getChannelDetails(channelId);

        if (channel.items?.length) {
            const influencerRepository = new InfluencerRepository();
            const influencerPlatformRepository = new InfluencerPlatformRepository();

            const item = channel.items[0];

            const influencer = await influencerRepository.create({
                display_name: item.snippet?.title || '',
                language: '',
                country: item.snippet?.country || '',
            });

            await influencerPlatformRepository.create({
                influencer_id: influencer.id!,
                followers: item.statistics?.subscriberCount ? parseInt(item.statistics.subscriberCount) : undefined,
                joined_platform: item.snippet?.publishedAt || new Date(),
                platform: Platform.YOUTUBE,
                media_count: item.statistics?.videoCount ? parseInt(item.statistics.videoCount) : undefined,
                number_of_views: item.statistics?.viewCount ? parseInt(item.statistics.viewCount) : undefined,
                platform_id: item.id!,
            });
        }
    }

    async importPendingVideos() {
        const provider = new YoutubeProvider();

        const r = new YouTubeImportedVideoRepository();

        let upd = 0;
        let pend = 0;

        do {
            upd = 0;
            const pending = await r.getPendingVideos(50);

            pend = pending.length;

            const ids = pending.map(p => p.video_id);

            // this.logger.info(`Found ${ids.length} videos to update`, ids);

            const details = await provider.getVideosDetails(ids);

            const found = new Set();

            if (details?.items) {
                for (const item of details.items) {
                    if (item.snippet && item.id) {
                        await r.updateVideoDetails(item.id, {
                            status: 'checked',
                            published_at: item.snippet.publishedAt || undefined,
                            comment_count: item.statistics?.commentCount ? parseInt(item.statistics.commentCount) : undefined,
                            favorite_count: item.statistics?.favoriteCount ? parseInt(item.statistics.favoriteCount) : undefined,
                            like_count: item.statistics?.likeCount ? parseInt(item.statistics.likeCount) : undefined,
                            view_count: item.statistics?.viewCount ? parseInt(item.statistics.viewCount) : undefined,
                            channel_id: item.snippet.channelId || undefined,
                        });

                        found.add(item.id);
                    }
                }
            }

            // get list of pending that was not returned in the details
            const notFound = pending.filter(p => !found.has(p.video_id));
            for (const p of notFound) {
                await r.updateVideoDetails(p.video_id, {
                    status: 'not_found',
                });
            }

            this.logger.info(`Updated ${upd} videos`);

            upd = details?.items?.length || 0;
        } while (pend > 0);
    }

    async importChannels() {
        const source = new YouTubeImportedVideoRepository();
        const provider = new YoutubeProvider();

        let channels: YouTubeImportedVideo[] = [];
        do {

            channels = await source.getChannelsToImport(50);
            console.log(`Channels to import: ${JSON.stringify(channels)}`)

            const details = await provider.getChannelsDetails(channels.map(c => c.channel_id));

            const influencerRepository = new InfluencerRepository();
            const influencerPlatformRepository = new InfluencerPlatformRepository();

            if (details.items?.length) {
                for (const item of details.items) {

                    const existing = await influencerPlatformRepository.findByPlatformAndPlatformId(Platform.YOUTUBE, item.id!);

                    if (existing) {
                        await influencerPlatformRepository.update(existing.id!, {
                            followers: item.statistics?.subscriberCount ? parseInt(item.statistics.subscriberCount) : undefined,
                            joined_platform: item.snippet?.publishedAt || new Date(),
                            media_count: item.statistics?.videoCount ? parseInt(item.statistics.videoCount) : undefined,
                            number_of_views: item.statistics?.viewCount ? parseInt(item.statistics.viewCount) : undefined,
                        });

                        this.logger.info(`Updated channel: ${item.snippet?.title || 'Unknown'} - ${item.id || 'Unknown'}`);

                        await source.markChannelAsImported(item.id!);
                        continue;
                    }

                    const influencer = await influencerRepository.create({
                        display_name: item.snippet?.title || '',
                        language: '',
                        country: item.snippet?.country || '',
                    });

                    await influencerPlatformRepository.create({
                        influencer_id: influencer.id!,
                        followers: item.statistics?.subscriberCount ? parseInt(item.statistics.subscriberCount) : undefined,
                        joined_platform: item.snippet?.publishedAt || new Date(),
                        platform: Platform.YOUTUBE,
                        media_count: item.statistics?.videoCount ? parseInt(item.statistics.videoCount) : undefined,
                        number_of_views: item.statistics?.viewCount ? parseInt(item.statistics.viewCount) : undefined,
                        platform_id: item.id!,
                    });

                    this.logger.info(`Created channel: ${item.snippet?.title || 'Unknown'} - ${item.id || 'Unknown'}`);

                    await source.markChannelAsImported(item.id!);
                }
            }
        } while (channels?.length > 0);
    }
}
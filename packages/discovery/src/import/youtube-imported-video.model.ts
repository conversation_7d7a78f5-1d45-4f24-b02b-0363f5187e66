/**
 * YouTube Imported Video model representing video data imported from YouTube API
 */
export interface YouTubeImportedVideo {
  video_id: string;
  channel_id: string;
  status?: string;
  view_count?: number;
  like_count?: number;
  favorite_count?: number;
  comment_count?: number;
  published_at?: Date;
  inserted_at?: Date;
}

/**
 * Data required to create a new YouTube imported video record
 */
export interface CreateYouTubeImportedVideoData {
  video_id: string;
  status: string;
  channel_id?: string;
  view_count?: number;
  like_count?: number;
  favorite_count?: number;
  comment_count?: number;
  published_at?: Date | string;
}

/**
 * Data for updating a YouTube imported video record
 */
export interface UpdateYouTubeImportedVideoData {
  status?: string;
  view_count?: number;
  like_count?: number;
  favorite_count?: number;
  comment_count?: number;
  published_at?: Date | string;
  channel_id?: string;
  imported?: boolean;
}

/**
 * Query filters for finding YouTube imported videos
 */
export interface YouTubeImportedVideoFilters {
  video_id?: string;
  channel_id?: string;
  status?: string;
  statuses?: string[];
  channel_ids?: string[];
  video_ids?: string[];
  minViewCount?: number;
  maxViewCount?: number;
  minLikeCount?: number;
  maxLikeCount?: number;
  minCommentCount?: number;
  maxCommentCount?: number;
  publishedAfter?: Date | string;
  publishedBefore?: Date | string;
  insertedAfter?: Date | string;
  insertedBefore?: Date | string;
  limit?: number;
  offset?: number;
  orderBy?: 'video_id' | 'channel_id' | 'view_count' | 'like_count' | 'comment_count' | 'published_at' | 'inserted_at';
  orderDirection?: 'asc' | 'desc';
}

/**
 * Video status enum
 */
export enum VideoStatus {
  PUBLIC = 'public',
  PRIVATE = 'private',
  UNLISTED = 'unlisted',
  DELETED = 'deleted',
  REMOVED = 'removed',
  UNAVAILABLE = 'unavailable',
}

/**
 * Batch insert data for multiple videos
 */
export interface BatchInsertYouTubeVideosData {
  videos: CreateYouTubeImportedVideoData[];
  onConflict?: 'ignore' | 'update';
}

/**
 * Statistics for YouTube imported videos
 */
export interface YouTubeVideoStats {
  totalVideos: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  statusBreakdown: Array<{ status: string; count: number }>;
  channelBreakdown: Array<{ channel_id: string; count: number; totalViews: number }>;
  averageViewCount: number;
  averageLikeCount: number;
  averageCommentCount: number;
}

/**
 * Video metrics summary
 */
export interface VideoMetricsSummary {
  video_id: string;
  channel_id: string;
  totalEngagement: number; // likes + comments
  engagementRate: number; // (likes + comments) / views
  viewsPerDay?: number; // views divided by days since published
  status: string;
  published_at?: Date;
}

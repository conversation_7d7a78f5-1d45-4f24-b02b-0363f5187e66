import { Knex } from 'knex';
import { Logger } from '../common/logger';
import {
  YouTubeImportedVideo,
  CreateYouTubeImportedVideoData,
  UpdateYouTubeImportedVideoData,
  YouTubeImportedVideoFilters,
  BatchInsertYouTubeVideosData,
  YouTubeVideoStats,
  VideoMetricsSummary,
  VideoStatus,
} from './youtube-imported-video.model';
import {DatabaseService} from "../database/database.service";

export class YouTubeImportedVideoRepository {
  private readonly tableName = 'youtube_imported_videos';

  constructor() {
  }

  /**
   * Insert a new YouTube imported video record
   */
  async insert(data: CreateYouTubeImportedVideoData): Promise<YouTubeImportedVideo> {
    try {
      // Convert published_at to Date if it's a string
      const processedData = {
        ...data,
        published_at: data.published_at ? 
          (typeof data.published_at === 'string' ? new Date(data.published_at) : data.published_at) 
          : undefined,
      };
      
      const [video] = await DatabaseService.getInstance().k()(this.tableName)
        .insert(processedData)
        .returning('*');

      return video;
    } catch (error) {
      throw error;
    }
  }

  async batchInsert(data: BatchInsertYouTubeVideosData): Promise<void> {
    try {
      await DatabaseService.getInstance().k()(this.tableName)
        .insert(data.videos)
        .onConflict('video_id')
        .ignore();
    } catch (error) {
      throw error;
    }
  }

  async getPendingVideos(limit: number = 50): Promise<YouTubeImportedVideo[]> {
    try {
      const videos = await DatabaseService.getInstance().k()(this.tableName)
        .where('status', 'pending')
        .limit(limit);

      return videos;
    } catch (error) {
      throw error;
    }
  }

  async updateStatusForVideos(videoIds: string[], status: string): Promise<void> {
    try {
      await DatabaseService.getInstance().k()(this.tableName)
        .whereIn('video_id', videoIds)
        .update({ status });
    } catch (error) {
      throw error;
    }
  }

  async updateVideoDetails(videoId: string, data: UpdateYouTubeImportedVideoData): Promise<void> {
    try {
      await DatabaseService.getInstance().k()(this.tableName)
        .where('video_id', videoId)
        .update(data);
    } catch (error) {
      throw error;
    }
  }

  // add method to retrieve channels to import (imported=false and status=checked)
  async getChannelsToImport(limit: number = 50): Promise<YouTubeImportedVideo[]> {
    try {
      const videos = await DatabaseService.getInstance().k()(this.tableName)
        .where('imported', false)
        .where('status', 'checked')
        .limit(limit);

      return videos;
    } catch (error) {
      throw error;
    }
  }
}
